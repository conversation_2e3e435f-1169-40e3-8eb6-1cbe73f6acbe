<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalAnalysisMapper">

    <!-- 获取提案概览统计数据（按时间维度） -->
    <select id="getProposalOverviewByTimeDimension" resultType="com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO">
        SELECT
            #{dto.year} AS year,
            -- 所有提案的数量
            COUNT(*) AS totalCount,
            -- 待立案的数量（WAIT_PUT_ON状态）
            SUM(CASE WHEN case_filing = 'WAIT_PUT_ON' THEN 1 ELSE 0 END) AS waitFilingCount,
            -- 已立案的数量（包含"立案、待办理、已并案、办结状态"：PUT_ON、WAIT_HANDLE、MERGED、FINISH）
            SUM(CASE
                WHEN case_filing IN ('PUT_ON', 'WAIT_HANDLE', 'MERGED', 'FINISH') THEN 1
                ELSE 0
            END) AS filedCount,
            -- 已办结的数量（FINISH状态）
            SUM(CASE WHEN case_filing = 'FINISH' THEN 1 ELSE 0 END) AS finishedCount
        FROM proposal
        WHERE del_flag = false
            <if test="dto.year != null">
                AND year = #{dto.year}
            </if>
            <if test="dto.startDate != null and dto.endDate != null">
                AND register_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
    </select>

    <!-- 获取提案状态统计数据 -->
    <select id="getProposalStatusStatistics" resultType="java.util.Map">
        SELECT
            case_filing AS statusCode,
            COUNT(*) AS count
        FROM proposal
        WHERE del_flag = false
            <if test="dto.year != null">
                AND year = #{dto.year}
            </if>
            <if test="dto.startDate != null and dto.endDate != null">
                AND register_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
        GROUP BY case_filing
        ORDER BY case_filing
    </select>

</mapper>
