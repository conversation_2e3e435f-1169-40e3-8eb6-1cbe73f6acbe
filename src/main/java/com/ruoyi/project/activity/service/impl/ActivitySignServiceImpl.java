package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.ActivitySign;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.dto.ActivitySignAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignListDTO;
import com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO;
import com.ruoyi.project.activity.domain.vo.ActivitySignVO;
import com.ruoyi.project.activity.mapper.ActivityBasicinfoMapper;
import com.ruoyi.project.activity.mapper.ActivityParticipantsMapper;
import com.ruoyi.project.activity.mapper.ActivitySignMapper;
import com.ruoyi.project.activity.service.IActivitySignDetailService;
import com.ruoyi.project.activity.service.IActivitySignService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动签到服务实现
 */
@Service
public class ActivitySignServiceImpl extends ServiceImpl<ActivitySignMapper, ActivitySign> implements IActivitySignService {

    @Autowired
    private ActivityBasicinfoMapper activityBasicinfoMapper;

    @Autowired
    private ActivityParticipantsMapper activityParticipantsMapper;

    @Autowired
    private IActivitySignDetailService activitySignDetailService;

    @Override
    public IPage<ActivitySignVO> getSignListByActivityId(ActivitySignListDTO dto) {
        // 创建分页对象
        Page<ActivitySign> page = new Page<>(dto.getPageNum(), dto.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<ActivitySign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySign::getActivityPkid, dto.getActivityPkid());

        // 如果有人员姓名查询条件，则添加模糊查询
        if (StringUtils.isNotEmpty(dto.getPepoleName())) {
            queryWrapper.like(ActivitySign::getPepoleName, dto.getPepoleName());
        }

        // 按签到开始时间降序排列
        queryWrapper.orderByDesc(ActivitySign::getSignBeginDate);

        // 执行分页查询
        IPage<ActivitySign> signPage = this.page(page, queryWrapper);

        // 如果没有数据，返回空分页对象
        if (signPage.getRecords() == null || signPage.getRecords().isEmpty()) {
            return new Page<ActivitySignVO>(dto.getPageNum(), dto.getPageSize());
        }

        // 查询活动信息
        ActivityBasicinfo activityBasicinfo = activityBasicinfoMapper.selectOne(
                new LambdaQueryWrapper<ActivityBasicinfo>()
                        .eq(ActivityBasicinfo::getId, dto.getActivityPkid())
        );

        // 转换为VO对象
        List<ActivitySignVO> voList = signPage.getRecords().stream().map(sign -> {
            ActivitySignVO vo = new ActivitySignVO();
            BeanUtils.copyProperties(sign, vo);

            // 设置活动标题
            if (activityBasicinfo != null) {
                vo.setActivityTitle(activityBasicinfo.getTitle());
            }

            // 计算统计数据
            calculateSignStatistics(vo, sign.getId().toString());

            return vo;
        }).collect(Collectors.toList());

        // 创建VO分页对象并设置数据
        Page<ActivitySignVO> voPage = new Page<>(signPage.getCurrent(), signPage.getSize(), signPage.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addActivitySign(ActivitySignAddDTO dto) {
        // 查询活动信息
        ActivityBasicinfo activityBasicinfo = activityBasicinfoMapper.selectOne(
                new LambdaQueryWrapper<ActivityBasicinfo>()
                        .eq(ActivityBasicinfo::getId, dto.getActivityPkid())
        );

        if (activityBasicinfo == null) {
            throw new RuntimeException("活动不存在");
        }

        // 创建签到记录
        ActivitySign activitySign = new ActivitySign();
        String signPkid = UUID.randomUUID().toString(); // 生成UUID
        activitySign.setPkid(signPkid);
        activitySign.setActivityPkid(dto.getActivityPkid());
        activitySign.setSignBeginDate(dto.getSignBeginDate());
        activitySign.setSignEndDate(dto.getSignEndDate());
        activitySign.setReason(dto.getReason());

        // 设置创建人信息
        String username = SecurityUtils.getUsername();
        activitySign.setPepoleName(username);

        // 保存签到记录
        boolean saveResult = this.save(activitySign);

        if (saveResult) {
            // 查询活动参与人员
            List<ActivityParticipants> participants = activityParticipantsMapper.selectList(
                new LambdaQueryWrapper<ActivityParticipants>()
                    .eq(ActivityParticipants::getActivityPkid, dto.getActivityPkid())
            );

            if (participants != null && !participants.isEmpty()) {
                List<ActivitySignDetail> signDetails = new ArrayList<>();

                // 为每个参与人员创建签到明细记录
                for (ActivityParticipants participant : participants) {
                    ActivitySignDetail detail = new ActivitySignDetail();
                    detail.setPkid(UUID.randomUUID().toString());
                    // 修复：使用保存后的签到记录的自增主键ID作为关联字段
                    detail.setSignPkid(activitySign.getId().toString());
                    detail.setPepolePkid(participant.getPepolePkid());
                    detail.setActivityPkid(dto.getActivityPkid());
                    detail.setBeginDate(activitySign.getSignBeginDate());
                    detail.setIsSign("0"); // 默认未签到
                    detail.setIsLeave("0"); // 默认未请假

                    // 根据参与人员的参加状态设置请假状态
                    if ("0".equals(participant.getIsAttend()) || "2".equals(participant.getIsAttend())) {
                        detail.setIsLeave("1"); // 已请假
                        detail.setReason(participant.getNoAttendReason());
                    }

                    // 设置负责人标志
                    detail.setIsLeader(participant.getIsLeader());

                    // 设置区域ID
                    detail.setRegionId(participant.getRegionId());

                    signDetails.add(detail);
                }

                // 批量保存签到明细
                if (!signDetails.isEmpty()) {
                    // 使用IService的批量保存方法
                    return activitySignDetailService.saveBatch(signDetails);
                }
            }
        }

        return saveResult;
    }

    /**
     * 计算签到统计数据
     *
     * @param vo 活动签到VO
     * @param signId 签到记录ID
     */
    private void calculateSignStatistics(ActivitySignVO vo, String signId) {
        // 查询该签到记录的所有签到明细
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getSignPkid, signId);

        List<ActivitySignDetail> signDetails = activitySignDetailService.list(queryWrapper);

        if (signDetails != null && !signDetails.isEmpty()) {
            // 计算总人数
            int totalPeople = signDetails.size();

            // 计算已签到人数（is_sign = '1'）
            long signedPeople = signDetails.stream()
                    .filter(detail -> "1".equals(detail.getIsSign()))
                    .count();

            // 计算未签到人数
            long unsignedPeople = totalPeople - signedPeople;

            // 设置统计数据
            vo.setTotalPeople(String.valueOf(totalPeople));
            vo.setSignedPeople(String.valueOf(signedPeople));
            vo.setUnsignedPeople(String.valueOf(unsignedPeople));
        } else {
            // 如果没有签到明细，设置为0
            vo.setTotalPeople("0");
            vo.setSignedPeople("0");
            vo.setUnsignedPeople("0");
        }
    }
}