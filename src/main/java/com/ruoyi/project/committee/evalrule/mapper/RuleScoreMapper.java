package com.ruoyi.project.committee.evalrule.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Mapper
public interface RuleScoreMapper extends BaseMapper<RuleScore> {

    default IPage<RuleScore> selectRuleScorePage(Page<RuleScore> page, RuleScorePageDto ruleScorePageDto) {
        return selectPage(page, new LambdaQueryWrapper<RuleScore>()
                .select(RuleScore.class,field -> !field.getProperty().equals("scoreDetail"))
                .eq(RuleScore::getYear, ruleScorePageDto.getYear())
                .like(ObjectUtil.isNotEmpty(ruleScorePageDto.getCommitteeName()), RuleScore::getUserName, ruleScorePageDto.getCommitteeName())
                .like(ObjectUtil.isNotEmpty(ruleScorePageDto.getCommitteeNumber()), RuleScore::getNumberId, ruleScorePageDto.getCommitteeNumber())
                .orderByDesc(RuleScore::getTotalScore)
        );
    };

    @Options(statementType = StatementType.CALLABLE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer upsertRuleScoresBatch(@Param("ruleScoreList") List<RuleScore> ruleScoreList);
}
