package com.ruoyi.project.committee.evalrule.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.redis.RedisCache;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoEditDto;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleScorePageVo;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


/**
 * 规则信息 控制器
 */
@Api(tags = "规则信息管理")
@RestController
@RequestMapping("/committee/evalrule")
public class RuleInfoController extends BaseController {

    @Autowired
    private IRuleInfoService ruleInfoService;

    @Resource
    private IRuleScoreService ruleScoreService;

    @Resource
    private RedisCache redisCache;

    /**
     * 查询规则信息树形列表
     */
    @ApiOperation("查询规则信息树形列表")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:list')")
    @GetMapping("/tree")
    public TableDataInfo tree() {
        return getDataTable(ruleInfoService.selectRuleInfoList());
    }

    /**
     * 获取规则信息详细信息（包含树形结构）
     */
    @ApiOperation("获取规则信息详细信息（包含树形结构）")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:query')")
    @GetMapping("/{pkid}")
    public AjaxResult getInfo(@PathVariable("pkid") String pkid) {
        return success(ruleInfoService.selectRuleInfoById(pkid));
    }

    /**
     * 新增规则信息
     */
    @ApiOperation("保存规则信息")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:add')")
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdateRule(@RequestBody RuleInfoEditDto editDto) {
        return AjaxResult.success(ruleInfoService.saveOrUpdateRule(editDto));
    }


    /**
     * 生成新一年的规则（自动复用前一年的规则）
     */
    @ApiOperation("生成新一年的规则")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:gen')")
    @PostMapping("/generate")
    public AjaxResult generateNewYear() {
        String newRuleId = ruleInfoService.generateNewYearRule();
        return success(newRuleId);
    }

    /**
     * 获取委员分数列表
     * @param pageDto pageDto
     * @return result
     */
    @PostMapping("/score/page")
    @PreAuthorize("@ss.hasAnyRoles('admin,super')")
    public TableDataInfo selectScorePage(@RequestBody RuleScorePageDto pageDto) {
        IPage<RuleScorePageVo> pageResult = ruleScoreService.selectRuleScorePage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 获取委员分数详情
     * @param id id
     * @return result
     */
    @GetMapping("/score/detail")
    @PreAuthorize("@ss.hasAnyRoles('admin,super')")
    public AjaxResult getScoreDetail(@RequestParam("id") String id) {
        return success(ruleScoreService.getScoreDetailById(id));
    }

    /**
     * 计算委员分数
     * @param year year
     * @return result
     */
    @PostMapping("/calculateScore")
    @PreAuthorize("@ss.hasAnyRoles('admin,super')")
    public AjaxResult calculateScore(@RequestParam("year") Integer year) {
        String redisKey = "calculateScore:" + year;

        if (redisCache.hasKey(redisKey)) {
            return redisCache.getCacheObject(redisKey);
        }

        AjaxResult initialResult = AjaxResult.success("计算中，请稍后查询");
        initialResult.put("nextQueryMinutes", 3);
        redisCache.setCacheObject(redisKey, initialResult, 5, TimeUnit.MINUTES);

        CompletableFuture.runAsync(() -> {
            try {
                Integer count = ruleScoreService.calculateScore(year);
                AjaxResult successResult = AjaxResult.success("计算完成，请刷新界面", count);
                redisCache.setCacheObject(redisKey, successResult, 5, TimeUnit.MINUTES);
            } catch (Exception e) {
                AjaxResult errorResult = AjaxResult.error("计算失败，请联系管理员 ");
                redisCache.setCacheObject(redisKey, errorResult, 5, TimeUnit.MINUTES);
            }
        });

        return initialResult;
    }

    /**
     * 获取委员分数计算结果
     * @param year year
     * @return result
     */
    @GetMapping("/getCalculateResult")
    @PreAuthorize("@ss.hasAnyRoles('admin,super')")
    public AjaxResult getCalculateResult(@RequestParam("year") Integer year) {
        String redisKey = "calculateScore:" + year;

        AjaxResult cacheObject = redisCache.getCacheObject(redisKey);

        return Optional.ofNullable(cacheObject)
                .orElse(AjaxResult.success("无计算任务！"));
    }

}
