package com.ruoyi.project.committee.evalrule.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RuleScorePageVo {

    private String id;

    @ApiModelProperty(value = "年度" )
    private Integer year;

    @ApiModelProperty(value = "委员姓名")
    private String userName;

    @ApiModelProperty(value = "委员编号")
    private String numberId;

    @ApiModelProperty(value = "职位")
    private String unitPost;

    @ApiModelProperty(value = "基础分")
    private Integer basicScore;

    @ApiModelProperty(value = "奖励分")
    private Integer rewardScore;

    @ApiModelProperty(value = "总分")
    private Integer totalScore;
}
