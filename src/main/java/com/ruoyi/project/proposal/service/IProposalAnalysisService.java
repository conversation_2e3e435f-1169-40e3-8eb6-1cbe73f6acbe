package com.ruoyi.project.proposal.service;

import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.domain.vo.ProposalStatusStatisticsVO;

/**
 * 提案分析服务接口
 */
public interface IProposalAnalysisService {

    /**
     * 获取提案概览统计数据
     *
     * @param dto 查询参数
     * @return 提案概览统计数据
     */
    ProposalAnalysisOverviewVO getProposalOverview(ProposalAnalysisDTO dto);
}
