package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.mapper.ProposalAnalysisMapper;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 提案分析服务实现类
 */
@Slf4j
@Service
public class ProposalAnalysisServiceImpl implements IProposalAnalysisService {

    @Resource
    private ProposalAnalysisMapper proposalAnalysisMapper;

    @Override
    public ProposalAnalysisOverviewVO getProposalOverview(ProposalAnalysisDTO dto) {
        log.info("获取提案概览统计数据，年份：{}", dto.getYear());
        
        ProposalAnalysisOverviewVO result = proposalAnalysisMapper.getProposalOverview(dto.getYear());
        
        // 如果查询结果为空，返回默认值
        if (result == null) {
            result = new ProposalAnalysisOverviewVO();
            result.setYear(dto.getYear());
            result.setTotalCount(0L);
            result.setWaitFilingCount(0L);
            result.setFiledCount(0L);
            result.setFinishedCount(0L);
        }
        
        log.info("提案概览统计完成，总数：{}，待立案：{}，已立案：{}，已办结：{}", 
                result.getTotalCount(), result.getWaitFilingCount(), 
                result.getFiledCount(), result.getFinishedCount());
        
        return result;
    }
}
