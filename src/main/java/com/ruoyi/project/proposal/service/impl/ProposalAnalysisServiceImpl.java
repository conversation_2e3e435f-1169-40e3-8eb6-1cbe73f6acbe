package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.common.enums.proposal.TimeDimensionEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.mapper.ProposalAnalysisMapper;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 提案分析服务实现类
 */
@Slf4j
@Service
public class ProposalAnalysisServiceImpl implements IProposalAnalysisService {

    @Resource
    private ProposalAnalysisMapper proposalAnalysisMapper;

    @Override
    public ProposalAnalysisOverviewVO getProposalOverview(ProposalAnalysisDTO dto) {
        log.info("获取提案概览统计数据，年份：{}，时间维度：{}", dto.getYear(), dto.getTimeDimension());

        // 参数验证和预处理
        validateAndPreprocessParams(dto);

        ProposalAnalysisOverviewVO result;

        // 根据时间维度选择不同的查询方法
        if (TimeDimensionEnum.YEAR.equals(dto.getTimeDimension())) {
            result = proposalAnalysisMapper.getProposalOverview(dto.getYear());
        } else {
            result = proposalAnalysisMapper.getProposalOverviewByTimeDimension(dto);
        }

        // 如果查询结果为空，返回默认值
        if (result == null) {
            result = createDefaultResult(dto);
        }

        // 设置时间维度相关信息
        enrichResultWithTimeInfo(result, dto);

        log.info("提案概览统计完成，总数：{}，待立案：{}，已立案：{}，已办结：{}",
                result.getTotalCount(), result.getWaitFilingCount(),
                result.getFiledCount(), result.getFinishedCount());

        return result;
    }

    /**
     * 参数验证和预处理
     */
    private void validateAndPreprocessParams(ProposalAnalysisDTO dto) {
        if (dto.getTimeDimension() == null) {
            dto.setTimeDimension(TimeDimensionEnum.YEAR);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(dto.getYear(), 0, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        switch (dto.getTimeDimension()) {
            case WEEK:
                if (dto.getWeek() == null) {
                    throw new ServiceException("选择周维度时，周数不能为空");
                }
                if (dto.getWeek() < 1 || dto.getWeek() > 53) {
                    throw new ServiceException("周数必须在1-53之间");
                }
                // 计算指定周的开始和结束时间
                calendar.set(Calendar.WEEK_OF_YEAR, dto.getWeek());
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                dto.setStartDate(calendar.getTime());
                calendar.add(Calendar.DAY_OF_WEEK, 6);
                dto.setEndDate(calendar.getTime());
                break;

            case MONTH:
                if (dto.getMonth() == null) {
                    throw new ServiceException("选择月维度时，月份不能为空");
                }
                if (dto.getMonth() < 1 || dto.getMonth() > 12) {
                    throw new ServiceException("月份必须在1-12之间");
                }
                // 计算指定月的开始和结束时间
                calendar.set(Calendar.MONTH, dto.getMonth() - 1);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(calendar.getTime());
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                dto.setEndDate(calendar.getTime());
                break;

            case QUARTER:
                if (dto.getQuarter() == null) {
                    throw new ServiceException("选择季度维度时，季度不能为空");
                }
                if (dto.getQuarter() < 1 || dto.getQuarter() > 4) {
                    throw new ServiceException("季度必须在1-4之间");
                }
                // 计算指定季度的开始和结束时间
                int startMonth = (dto.getQuarter() - 1) * 3;
                calendar.set(Calendar.MONTH, startMonth);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(calendar.getTime());
                calendar.set(Calendar.MONTH, startMonth + 2);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                dto.setEndDate(calendar.getTime());
                break;

            case YEAR:
                // 年维度不需要额外处理
                calendar.set(Calendar.MONTH, 0);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(calendar.getTime());
                calendar.set(Calendar.MONTH, 11);
                calendar.set(Calendar.DAY_OF_MONTH, 31);
                dto.setEndDate(calendar.getTime());
                break;
        }
    }

    /**
     * 创建默认结果
     */
    private ProposalAnalysisOverviewVO createDefaultResult(ProposalAnalysisDTO dto) {
        ProposalAnalysisOverviewVO result = new ProposalAnalysisOverviewVO();
        result.setYear(dto.getYear());
        result.setTotalCount(0L);
        result.setWaitFilingCount(0L);
        result.setFiledCount(0L);
        result.setFinishedCount(0L);
        return result;
    }

    /**
     * 丰富结果中的时间信息
     */
    private void enrichResultWithTimeInfo(ProposalAnalysisOverviewVO result, ProposalAnalysisDTO dto) {
        result.setTimeDimension(dto.getTimeDimension());
        result.setTimeDimensionDesc(dto.getTimeDimension().getDescription());
        result.setStartDate(dto.getStartDate());
        result.setEndDate(dto.getEndDate());

        // 生成时间范围描述
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String timeRangeDesc;

        switch (dto.getTimeDimension()) {
            case WEEK:
                timeRangeDesc = String.format("%d年第%d周 (%s ~ %s)",
                    dto.getYear(), dto.getWeek(),
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case MONTH:
                timeRangeDesc = String.format("%d年%d月 (%s ~ %s)",
                    dto.getYear(), dto.getMonth(),
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case QUARTER:
                timeRangeDesc = String.format("%d年第%d季度 (%s ~ %s)",
                    dto.getYear(), dto.getQuarter(),
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case YEAR:
            default:
                timeRangeDesc = String.format("%d年 (%s ~ %s)",
                    dto.getYear(),
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
        }

        result.setTimeRangeDesc(timeRangeDesc);
    }
}
