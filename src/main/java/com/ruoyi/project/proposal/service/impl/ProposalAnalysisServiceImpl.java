package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.common.enums.proposal.TimeDimensionEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.mapper.ProposalAnalysisMapper;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 提案分析服务实现类
 */
@Slf4j
@Service
public class ProposalAnalysisServiceImpl implements IProposalAnalysisService {

    @Resource
    private ProposalAnalysisMapper proposalAnalysisMapper;

    @Override
    public ProposalAnalysisOverviewVO getProposalOverview(ProposalAnalysisDTO dto) {
        log.info("=== 开始获取提案概览统计数据 ===");
        log.info("输入参数 - 时间维度：{}", dto.getTimeDimension());

        // 参数验证和预处理
        validateAndPreprocessParams(dto);

        log.info("预处理后 - 时间维度：{}，开始时间：{}，结束时间：{}，年份：{}",
                dto.getTimeDimension(), dto.getStartDate(), dto.getEndDate(), dto.getYear());

        // 使用统一的查询方法
        ProposalAnalysisOverviewVO result = proposalAnalysisMapper.getProposalOverviewByTimeDimension(dto);

        log.info("数据库查询结果：{}", result);

        // 如果查询结果为空，返回默认值
        if (result == null) {
            log.warn("数据库查询结果为空，创建默认结果");
            result = createDefaultResult(dto);
        }

        // 设置时间维度相关信息
        enrichResultWithTimeInfo(result, dto);

        log.info("提案概览统计完成，时间范围：{}，总数：{}，待立案：{}，已立案：{}，已办结：{}",
                result.getTimeRangeDesc(), result.getTotalCount(), result.getWaitFilingCount(),
                result.getFiledCount(), result.getFinishedCount());
        log.info("=== 提案概览统计数据获取完成 ===");

        return result;
    }

    /**
     * 参数验证和预处理
     */
    private void validateAndPreprocessParams(ProposalAnalysisDTO dto) {
        if (dto.getTimeDimension() == null || dto.getTimeDimension().trim().isEmpty()) {
            dto.setTimeDimension("year");
        }

        // 获取当前时间
        Calendar now = Calendar.getInstance();
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();

        // 设置时间为当天的开始和结束
        startCal.set(Calendar.HOUR_OF_DAY, 0);
        startCal.set(Calendar.MINUTE, 0);
        startCal.set(Calendar.SECOND, 0);
        startCal.set(Calendar.MILLISECOND, 0);

        endCal.set(Calendar.HOUR_OF_DAY, 23);
        endCal.set(Calendar.MINUTE, 59);
        endCal.set(Calendar.SECOND, 59);
        endCal.set(Calendar.MILLISECOND, 999);

        switch (dto.getTimeDimension().toLowerCase()) {
            case "week":
                // 前7天：从7天前到今天
                startCal.add(Calendar.DAY_OF_YEAR, -6); // 前6天 + 今天 = 7天
                dto.setStartDate(startCal.getTime());
                dto.setEndDate(endCal.getTime());
                break;

            case "month":
                // 当前月：从本月1号到本月最后一天
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.set(Calendar.DAY_OF_MONTH, endCal.getActualMaximum(Calendar.DAY_OF_MONTH));
                dto.setEndDate(endCal.getTime());
                break;

            case "quarter":
                // 前3个月：从3个月前的1号到上个月的最后一天
                startCal.add(Calendar.MONTH, -2); // 前3个月
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.add(Calendar.MONTH, -1); // 上个月
                endCal.set(Calendar.DAY_OF_MONTH, endCal.getActualMaximum(Calendar.DAY_OF_MONTH));
                dto.setEndDate(endCal.getTime());
                break;

            case "year":
            default:
                // 当前年：从1月1号到12月31号
                startCal.set(Calendar.MONTH, Calendar.JANUARY);
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.set(Calendar.MONTH, Calendar.DECEMBER);
                endCal.set(Calendar.DAY_OF_MONTH, 31);
                dto.setEndDate(endCal.getTime());
                break;
        }

        // 设置年份
        dto.setYear(now.get(Calendar.YEAR));
    }

    /**
     * 创建默认结果
     */
    private ProposalAnalysisOverviewVO createDefaultResult(ProposalAnalysisDTO dto) {
        ProposalAnalysisOverviewVO result = new ProposalAnalysisOverviewVO();
        result.setYear(dto.getYear());
        result.setTotalCount(0L);
        result.setWaitFilingCount(0L);
        result.setFiledCount(0L);
        result.setFinishedCount(0L);
        return result;
    }

    /**
     * 丰富结果中的时间信息
     */
    private void enrichResultWithTimeInfo(ProposalAnalysisOverviewVO result, ProposalAnalysisDTO dto) {
        TimeDimensionEnum dimensionEnum = TimeDimensionEnum.getByValue(dto.getTimeDimension());

        result.setTimeDimension(dto.getTimeDimension());
        result.setTimeDimensionDesc(dimensionEnum.getDescription());
        result.setStartDate(dto.getStartDate());
        result.setEndDate(dto.getEndDate());

        // 生成时间范围描述
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String timeRangeDesc;

        switch (dto.getTimeDimension().toLowerCase()) {
            case "week":
                timeRangeDesc = String.format("前7天 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "month":
                timeRangeDesc = String.format("当前月 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "quarter":
                timeRangeDesc = String.format("前3个月 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "year":
            default:
                timeRangeDesc = String.format("当前年 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
        }

        result.setTimeRangeDesc(timeRangeDesc);
    }
}
