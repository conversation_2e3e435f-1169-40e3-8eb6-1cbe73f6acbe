package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.common.enums.proposal.TimeDimensionEnum;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.mapper.ProposalAnalysisMapper;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * 提案分析服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProposalAnalysisServiceImpl implements IProposalAnalysisService {

    private final ProposalAnalysisMapper proposalAnalysisMapper;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public ProposalAnalysisOverviewVO getProposalOverview(ProposalAnalysisDTO dto) {
        log.info("获取提案概览统计数据，时间维度：{}", dto.getTimeDimension());

        // 参数预处理
        preprocessParams(dto);

        // 查询数据
        ProposalAnalysisOverviewVO result = proposalAnalysisMapper.getProposalOverviewByTimeDimension(dto);
        if (result == null) {
            result = createDefaultResult(dto);
        }

        // 设置时间维度相关信息
        enrichResultWithTimeInfo(result, dto);

        log.info("提案概览统计完成，时间范围：{}，总数：{}", result.getTimeRangeDesc(), result.getTotalCount());
        return result;
    }

    /**
     * 参数预处理
     */
    private void preprocessParams(ProposalAnalysisDTO dto) {
        // 设置默认时间维度
        if (dto.getTimeDimension() == null || dto.getTimeDimension().trim().isEmpty()) {
            dto.setTimeDimension("year");
        }

        LocalDate now = LocalDate.now();
        dto.setYear(now.getYear());

        // 根据时间维度计算时间范围
        TimePeriod period = calculateTimePeriod(dto.getTimeDimension().toLowerCase(), now);
        dto.setStartDate(Date.from(period.start.atZone(ZoneId.systemDefault()).toInstant()));
        dto.setEndDate(Date.from(period.end.atZone(ZoneId.systemDefault()).toInstant()));
    }

    /**
     * 计算时间范围
     */
    private TimePeriod calculateTimePeriod(String timeDimension, LocalDate now) {
        LocalDateTime start, end;

        switch (timeDimension) {
            case "week":
                // 本周：从本周一到本周日
                start = now.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
                end = now.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY)).atTime(LocalTime.MAX);
                break;

            case "month":
                // 当前月：从本月1号到本月最后一天
                start = now.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
                end = now.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
                break;

            case "quarter":
                // 前3个月：从3个月前的1号到上个月的最后一天
                LocalDate threeMonthsAgo = now.minusMonths(2);
                LocalDate lastMonth = now.minusMonths(1);
                start = threeMonthsAgo.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
                end = lastMonth.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
                break;

            case "year":
            default:
                // 当前年：从1月1号到12月31号
                start = now.with(TemporalAdjusters.firstDayOfYear()).atStartOfDay();
                end = now.with(TemporalAdjusters.lastDayOfYear()).atTime(LocalTime.MAX);
                break;
        }

        return new TimePeriod(start, end);
    }

    /**
     * 创建默认结果
     */
    private ProposalAnalysisOverviewVO createDefaultResult(ProposalAnalysisDTO dto) {
        ProposalAnalysisOverviewVO result = new ProposalAnalysisOverviewVO();
        result.setYear(dto.getYear());
        result.setTotalCount(0L);
        result.setWaitFilingCount(0L);
        result.setFiledCount(0L);
        result.setFinishedCount(0L);
        return result;
    }

    /**
     * 丰富结果中的时间信息
     */
    private void enrichResultWithTimeInfo(ProposalAnalysisOverviewVO result, ProposalAnalysisDTO dto) {
        TimeDimensionEnum dimensionEnum = TimeDimensionEnum.getByValue(dto.getTimeDimension());

        result.setTimeDimension(dto.getTimeDimension());
        result.setTimeDimensionDesc(dimensionEnum.getDescription());
        result.setStartDate(dto.getStartDate());
        result.setEndDate(dto.getEndDate());

        // 生成时间范围描述
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String timeRangeDesc;

        switch (dto.getTimeDimension().toLowerCase()) {
            case "week":
                timeRangeDesc = String.format("本周 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "month":
                timeRangeDesc = String.format("当前月 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "quarter":
                timeRangeDesc = String.format("前3个月 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
            case "year":
            default:
                timeRangeDesc = String.format("当前年 (%s ~ %s)",
                    sdf.format(dto.getStartDate()), sdf.format(dto.getEndDate()));
                break;
        }

        result.setTimeRangeDesc(timeRangeDesc);
    }

    /**
     * 时间范围内部类
     */
    private static class TimePeriod {
        private final LocalDateTime start;
        private final LocalDateTime end;

        public TimePeriod(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
    }
}
