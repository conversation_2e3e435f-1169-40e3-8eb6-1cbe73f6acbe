package com.ruoyi.project.proposal.mapper;

import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提案分析数据访问层
 */
@Mapper
public interface ProposalAnalysisMapper {

    /**
     * 获取提案概览统计数据（按时间维度）
     *
     * @param dto 查询参数
     * @return 提案概览统计数据
     */
    ProposalAnalysisOverviewVO getProposalOverviewByTimeDimension(@Param("dto") ProposalAnalysisDTO dto);
}
