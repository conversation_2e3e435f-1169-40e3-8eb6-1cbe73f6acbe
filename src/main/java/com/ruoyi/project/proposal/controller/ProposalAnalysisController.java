package com.ruoyi.project.proposal.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 提案分析控制器
 */
@Slf4j
@Api(tags = "提案分析管理")
@RestController
@RequestMapping("/proposal/analysis")
public class ProposalAnalysisController extends BaseController {

    @Resource
    private IProposalAnalysisService proposalAnalysisService;

    /**
     * 获取提案概览统计数据
     * 支持按相对时间维度进行统计
     *
     * @param timeDimension 时间维度参数，可选值：week、month、quarter、year
     * @return 提案概览统计数据
     */
    @ApiOperation(value = "获取提案概览统计数据",
                  notes = "支持按相对时间维度进行统计：" +
                         "week-前7天，month-当前月，quarter-前3个月，year-当前年。" +
                         "如果不传timeDimension参数，默认返回当前年的数据。")
    @GetMapping("/overview")
    public AjaxResult getProposalOverview(
            @RequestParam(value = "timeDimension", defaultValue = "year") String timeDimension) {
        log.info("=== 获取提案概览统计数据 ===");
        log.info("接收到的timeDimension参数：{}", timeDimension);

        ProposalAnalysisDTO dto = new ProposalAnalysisDTO();
        dto.setTimeDimension(timeDimension);

        log.info("创建DTO对象，timeDimension：{}", dto.getTimeDimension());

        ProposalAnalysisOverviewVO result = proposalAnalysisService.getProposalOverview(dto);

        log.info("=== 请求处理完成 ===");
        return AjaxResult.success(result);
    }

    /**
     * 测试接口：获取原始数据用于调试
     */
    @ApiOperation(value = "测试接口：获取原始数据", notes = "用于调试，返回不同时间维度的原始参数")
    @GetMapping("/test")
    public AjaxResult testTimeCalculation(
            @RequestParam(value = "timeDimension", defaultValue = "year") String timeDimension) {
        log.info("=== 测试接口 - 时间计算 ===");

        ProposalAnalysisDTO dto = new ProposalAnalysisDTO();
        dto.setTimeDimension(timeDimension);

        // 只做参数预处理，不查询数据库
        if (dto.getTimeDimension() == null || dto.getTimeDimension().trim().isEmpty()) {
            dto.setTimeDimension("year");
        }

        // 获取当前时间
        java.util.Calendar now = java.util.Calendar.getInstance();
        java.util.Calendar startCal = java.util.Calendar.getInstance();
        java.util.Calendar endCal = java.util.Calendar.getInstance();

        // 设置时间为当天的开始和结束
        startCal.set(java.util.Calendar.HOUR_OF_DAY, 0);
        startCal.set(java.util.Calendar.MINUTE, 0);
        startCal.set(java.util.Calendar.SECOND, 0);
        startCal.set(java.util.Calendar.MILLISECOND, 0);

        endCal.set(java.util.Calendar.HOUR_OF_DAY, 23);
        endCal.set(java.util.Calendar.MINUTE, 59);
        endCal.set(java.util.Calendar.SECOND, 59);
        endCal.set(java.util.Calendar.MILLISECOND, 999);

        switch (dto.getTimeDimension().toLowerCase()) {
            case "week":
                startCal.add(java.util.Calendar.DAY_OF_YEAR, -6);
                dto.setStartDate(startCal.getTime());
                dto.setEndDate(endCal.getTime());
                break;
            case "month":
                startCal.set(java.util.Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.set(java.util.Calendar.DAY_OF_MONTH, endCal.getActualMaximum(java.util.Calendar.DAY_OF_MONTH));
                dto.setEndDate(endCal.getTime());
                break;
            case "quarter":
                startCal.add(java.util.Calendar.MONTH, -2);
                startCal.set(java.util.Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.add(java.util.Calendar.MONTH, -1);
                endCal.set(java.util.Calendar.DAY_OF_MONTH, endCal.getActualMaximum(java.util.Calendar.DAY_OF_MONTH));
                dto.setEndDate(endCal.getTime());
                break;
            case "year":
            default:
                startCal.set(java.util.Calendar.MONTH, java.util.Calendar.JANUARY);
                startCal.set(java.util.Calendar.DAY_OF_MONTH, 1);
                dto.setStartDate(startCal.getTime());
                endCal.set(java.util.Calendar.MONTH, java.util.Calendar.DECEMBER);
                endCal.set(java.util.Calendar.DAY_OF_MONTH, 31);
                dto.setEndDate(endCal.getTime());
                break;
        }

        dto.setYear(now.get(java.util.Calendar.YEAR));

        log.info("测试结果 - 时间维度：{}，开始时间：{}，结束时间：{}，年份：{}",
                dto.getTimeDimension(), dto.getStartDate(), dto.getEndDate(), dto.getYear());

        return AjaxResult.success(dto);
    }
}
