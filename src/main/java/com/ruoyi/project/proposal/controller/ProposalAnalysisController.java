package com.ruoyi.project.proposal.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 提案分析控制器
 */
@Api(tags = "提案分析管理")
@RestController
@RequestMapping("/proposal/analysis")
public class ProposalAnalysisController extends BaseController {

    @Resource
    private IProposalAnalysisService proposalAnalysisService;

    /**
     * 获取提案概览统计数据（POST方式）
     * 支持按相对时间维度进行统计
     *
     * @param dto 查询参数
     * @return 提案概览统计数据
     */
    @ApiOperation(value = "获取提案概览统计数据（POST方式）",
                  notes = "支持按相对时间维度进行统计：" +
                         "week-前7天，month-当前月，quarter-前3个月，year-当前年。" +
                         "返回所有提案数量、待立案数量、已立案数量、已办结数量等统计信息。")
    @PostMapping("/overview")
    public AjaxResult getProposalOverview(@RequestBody(required = false) ProposalAnalysisDTO dto) {
        // 如果请求体为空，创建默认DTO
        if (dto == null) {
            dto = new ProposalAnalysisDTO();
        }
        ProposalAnalysisOverviewVO result = proposalAnalysisService.getProposalOverview(dto);
        return AjaxResult.success(result);
    }

    /**
     * 获取提案概览统计数据（GET方式）
     * 支持按相对时间维度进行统计
     *
     * @param timeDimension 时间维度参数，可选值：week、month、quarter、year
     * @return 提案概览统计数据
     */
    @ApiOperation(value = "获取提案概览统计数据（GET方式）",
                  notes = "支持按相对时间维度进行统计：" +
                         "week-前7天，month-当前月，quarter-前3个月，year-当前年。" +
                         "如果不传timeDimension参数，默认返回当前年的数据。")
    @GetMapping("/overview")
    public AjaxResult getProposalOverviewByGet(
            @RequestParam(value = "timeDimension", defaultValue = "year") String timeDimension) {
        ProposalAnalysisDTO dto = new ProposalAnalysisDTO();
        dto.setTimeDimension(timeDimension);
        ProposalAnalysisOverviewVO result = proposalAnalysisService.getProposalOverview(dto);
        return AjaxResult.success(result);
    }
}
