package com.ruoyi.project.proposal.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.TimeDimensionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 提案分析查询DTO
 */
@Data
@ApiModel(value = "提案分析查询DTO")
public class ProposalAnalysisDTO {

    /**
     * 统计年份
     */
    @NotNull(message = "统计年份不能为空")
    @ApiModelProperty(value = "统计年份", required = true, example = "2024")
    private Integer year;

    /**
     * 时间维度（周、月、季、年）
     */
    @ApiModelProperty(value = "时间维度", example = "YEAR", notes = "WEEK-周, MONTH-月, QUARTER-季, YEAR-年")
    private TimeDimensionEnum timeDimension = TimeDimensionEnum.YEAR;

    /**
     * 开始时间（当选择周、月、季时使用）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间", example = "2024-01-01", notes = "当时间维度为周、月、季时必填")
    private Date startDate;

    /**
     * 结束时间（当选择周、月、季时使用）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间", example = "2024-12-31", notes = "当时间维度为周、月、季时必填")
    private Date endDate;

    /**
     * 季度（当选择季度时使用，1-4）
     */
    @ApiModelProperty(value = "季度", example = "1", notes = "当时间维度为季时使用，1-4")
    private Integer quarter;

    /**
     * 月份（当选择月份时使用，1-12）
     */
    @ApiModelProperty(value = "月份", example = "1", notes = "当时间维度为月时使用，1-12")
    private Integer month;

    /**
     * 周数（当选择周时使用，1-53）
     */
    @ApiModelProperty(value = "周数", example = "1", notes = "当时间维度为周时使用，1-53")
    private Integer week;
}
