package com.ruoyi.project.proposal.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 提案分析查询DTO
 */
@Data
@ApiModel(value = "提案分析查询DTO")
public class ProposalAnalysisDTO {

    /**
     * 时间维度值
     * week: 前7天
     * month: 当前月
     * quarter: 前3个月
     * year: 当前年
     */
    @ApiModelProperty(value = "时间维度", example = "year",
                     notes = "week-前7天, month-当前月, quarter-前3个月, year-当前年",
                     required = true)
    private String timeDimension = "year";

    /**
     * 统计开始时间（由后端根据timeDimension自动计算）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(hidden = true)
    private Date startDate;

    /**
     * 统计结束时间（由后端根据timeDimension自动计算）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(hidden = true)
    private Date endDate;

    /**
     * 统计年份（由后端根据当前时间自动计算）
     */
    @ApiModelProperty(hidden = true)
    private Integer year;
}
