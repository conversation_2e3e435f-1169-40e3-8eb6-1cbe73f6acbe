package com.ruoyi.project.proposal.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 提案分析查询DTO
 */
@Data
@ApiModel(value = "提案分析查询DTO")
public class ProposalAnalysisDTO {

    /**
     * 统计年份
     */
    @NotNull(message = "统计年份不能为空")
    @ApiModelProperty(value = "统计年份", required = true, example = "2024")
    private Integer year;
}
