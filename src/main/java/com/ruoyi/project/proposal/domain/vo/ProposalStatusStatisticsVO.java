package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 提案状态统计VO
 */
@Data
@ApiModel(value = "提案状态统计VO")
public class ProposalStatusStatisticsVO {

    /**
     * 统计年份
     */
    @ApiModelProperty(value = "统计年份")
    private Integer year;

    /**
     * 时间维度值
     */
    @ApiModelProperty(value = "时间维度值")
    private String timeDimension;

    /**
     * 时间维度描述
     */
    @ApiModelProperty(value = "时间维度描述")
    private String timeDimensionDesc;

    /**
     * 统计开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "统计开始时间")
    private Date startDate;

    /**
     * 统计结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "统计结束时间")
    private Date endDate;

    /**
     * 时间范围描述
     */
    @ApiModelProperty(value = "时间范围描述")
    private String timeRangeDesc;

    /**
     * 状态统计列表
     */
    @ApiModelProperty(value = "状态统计列表")
    private List<StatusCountItem> statusCounts;

    /**
     * 状态统计项
     */
    @Data
    @ApiModel(value = "状态统计项")
    public static class StatusCountItem {
        
        /**
         * 状态代码
         */
        @ApiModelProperty(value = "状态代码")
        private String statusCode;

        /**
         * 状态名称
         */
        @ApiModelProperty(value = "状态名称")
        private String statusName;

        /**
         * 数量
         */
        @ApiModelProperty(value = "数量")
        private Long count;

        /**
         * 百分比
         */
        @ApiModelProperty(value = "百分比")
        private Double percentage;

        public StatusCountItem() {}

        public StatusCountItem(String statusCode, String statusName, Long count) {
            this.statusCode = statusCode;
            this.statusName = statusName;
            this.count = count;
        }
    }
}
