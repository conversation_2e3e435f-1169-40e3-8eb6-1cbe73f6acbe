package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 提案分析概览VO
 */
@Data
@ApiModel(value = "提案分析概览VO")
public class ProposalAnalysisOverviewVO {

    /**
     * 所有提案的数量
     */
    @ApiModelProperty(value = "所有提案的数量")
    private Long totalCount;

    /**
     * 待立案的数量
     */
    @ApiModelProperty(value = "待立案的数量")
    private Long waitFilingCount;

    /**
     * 已立案的数量（包含"立案、待办理、已并案、办结状态"）
     */
    @ApiModelProperty(value = "已立案的数量")
    private Long filedCount;

    /**
     * 已办结的数量
     */
    @ApiModelProperty(value = "已办结的数量")
    private Long finishedCount;

    /**
     * 统计年份
     */
    @ApiModelProperty(value = "统计年份")
    private Integer year;
}
