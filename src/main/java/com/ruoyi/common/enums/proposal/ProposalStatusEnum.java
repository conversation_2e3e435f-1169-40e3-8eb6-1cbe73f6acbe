package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 提案状态枚举
 */
@Getter
public enum ProposalStatusEnum {

    /**
     * 已办结
     */
    FINISHED("FINISH", "已办结"),

    /**
     * 待立案
     */
    WAIT_FILING("WAIT_PUT_ON", "待立案"),

    /**
     * 已并案
     */
    MERGED("MERGED", "已并案"),

    /**
     * 已立案（包含"立案、待办理、已并案、办结状态"）
     */
    FILED("FILED", "已立案"),

    /**
     * 待办理
     */
    WAIT_HANDLE("WAIT_HANDLE", "待办理"),

    /**
     * 不立案
     */
    NOT_FILED("NOT_PUT_ON", "不立案"),

    /**
     * 已撤案
     */
    WITHDRAW("WITHDRAW", "已撤案"),

    /**
     * 立案
     */
    PUT_ON("PUT_ON", "立案");

    private final String code;
    private final String name;

    ProposalStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据代码获取枚举
     */
    public static ProposalStatusEnum getByCode(String code) {
        for (ProposalStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取已立案状态的代码列表（包含"立案、待办理、已并案、办结状态"）
     */
    public static String[] getFiledStatusCodes() {
        return new String[]{"PUT_ON", "WAIT_HANDLE", "MERGED", "FINISH"};
    }
}
