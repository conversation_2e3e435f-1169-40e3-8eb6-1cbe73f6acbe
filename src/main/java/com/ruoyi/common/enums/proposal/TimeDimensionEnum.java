package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 时间维度枚举
 */
@Getter
public enum TimeDimensionEnum {

    /**
     * 按周统计（前7天）
     */
    WEEK("week", "周"),

    /**
     * 按月统计（当前月）
     */
    MONTH("month", "月"),

    /**
     * 按季统计（前3个月）
     */
    QUARTER("quarter", "季"),

    /**
     * 按年统计（当前年）
     */
    YEAR("year", "年");

    private final String value;
    private final String description;

    TimeDimensionEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据value获取枚举
     */
    public static TimeDimensionEnum getByValue(String value) {
        for (TimeDimensionEnum dimension : values()) {
            if (dimension.getValue().equals(value)) {
                return dimension;
            }
        }
        return YEAR; // 默认返回年
    }
}
