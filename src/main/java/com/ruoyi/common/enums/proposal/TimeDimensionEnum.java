package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 时间维度枚举
 */
@Getter
public enum TimeDimensionEnum {

    /**
     * 按周统计
     */
    WEEK("周"),

    /**
     * 按月统计
     */
    MONTH("月"),

    /**
     * 按季统计
     */
    QUARTER("季"),

    /**
     * 按年统计
     */
    YEAR("年");

    private final String description;

    TimeDimensionEnum(String description) {
        this.description = description;
    }
}
